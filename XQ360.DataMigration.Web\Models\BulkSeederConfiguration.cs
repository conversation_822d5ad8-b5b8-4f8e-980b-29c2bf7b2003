using System.ComponentModel.DataAnnotations;

namespace XQ360.DataMigration.Web.Models;

/// <summary>
/// Configuration options for the bulk seeder functionality
/// </summary>
public class BulkSeederConfiguration
{
    public const string SectionName = "BulkSeeder";

    /// <summary>
    /// Default number of drivers to process when not specified
    /// </summary>
    [Range(1, 1000000)]
    public int DefaultDriversCount { get; set; } = 10000;

    /// <summary>
    /// Default number of vehicles to process when not specified
    /// </summary>
    [Range(1, 1000000)]
    public int DefaultVehiclesCount { get; set; } = 5000;

    /// <summary>
    /// Default batch size for bulk operations
    /// </summary>
    [Range(100, 100000)]
    public int DefaultBatchSize { get; set; } = 1000;

    /// <summary>
    /// Maximum allowed batch size
    /// </summary>
    [Range(1000, 100000)]
    public int MaxBatchSize { get; set; } = 50000;

    /// <summary>
    /// Timeout for SqlBulkCopy operations in seconds
    /// </summary>
    [Range(30, 3600)]
    public int BulkCopyTimeout { get; set; } = 300;

    /// <summary>
    /// Timeout for SQL commands in seconds
    /// </summary>
    [Range(30, 1800)]
    public int CommandTimeout { get; set; } = 120;

    /// <summary>
    /// Number of rows after which SqlBulkCopy sends a notification
    /// </summary>
    [Range(100, 10000)]
    public int NotifyAfter { get; set; } = 1000;

    /// <summary>
    /// Enable retry policy for transient failures
    /// </summary>
    public bool EnableRetry { get; set; } = true;

    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    [Range(1, 10)]
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Delay between retry attempts in seconds
    /// </summary>
    [Range(1, 60)]
    public int RetryDelaySeconds { get; set; } = 5;

    /// <summary>
    /// Enable validation of input data before processing
    /// </summary>
    public bool ValidationEnabled { get; set; } = true;

    /// <summary>
    /// Stop processing on first validation error
    /// </summary>
    public bool StopOnFirstError { get; set; } = false;

    /// <summary>
    /// Enable dealer-specific validation and scoping
    /// </summary>
    public bool DealerValidationEnabled { get; set; } = true;

    /// <summary>
    /// Require dealer selection before seeding operations
    /// </summary>
    public bool RequireDealerSelection { get; set; } = true;

    /// <summary>
    /// Default dealer ID to use when not specified (optional)
    /// </summary>
    public Guid? DefaultDealerId { get; set; }

    /// <summary>
    /// Default dealer name to use when not specified (optional)
    /// </summary>
    public string? DefaultDealerName { get; set; }

    /// <summary>
    /// Whether to clean up staging data after successful processing
    /// </summary>
    public bool CleanupStagingData { get; set; } = true;

    /// <summary>
    /// Use temporary tables instead of permanent staging tables
    /// </summary>
    public bool UseTempTables { get; set; } = true;

    /// <summary>
    /// Temporary table mode: SessionScoped or Global
    /// </summary>
    public string TempTableMode { get; set; } = "SessionScoped";

    /// <summary>
    /// Batch size for temporary table operations
    /// </summary>
    [Range(100, 50000)]
    public int TempTableBatchSize { get; set; } = 5000;

    /// <summary>
    /// Create indexes on temporary tables for performance
    /// </summary>
    public bool TempTableIndexes { get; set; } = true;

    /// <summary>
    /// Enable detailed logging of temporary table operations
    /// </summary>
    public bool LogTempTableOperations { get; set; } = true;

    /// <summary>
    /// Connection pool optimization settings for Phase 1.2.3
    /// Dedicated connection pools for staging vs production operations
    /// Min 10, Max 100 connections, 30-second idle timeout
    /// </summary>
    
    /// <summary>
    /// Minimum connections in the staging operations pool
    /// </summary>
    [Range(1, 50)]
    public int StagingPoolMinConnections { get; set; } = 10;

    /// <summary>
    /// Maximum connections in the staging operations pool
    /// </summary>
    [Range(10, 100)]
    public int StagingPoolMaxConnections { get; set; } = 100;

    /// <summary>
    /// Minimum connections in the production operations pool
    /// </summary>
    [Range(1, 50)]
    public int ProductionPoolMinConnections { get; set; } = 10;

    /// <summary>
    /// Maximum connections in the production operations pool
    /// </summary>
    [Range(10, 100)]
    public int ProductionPoolMaxConnections { get; set; } = 100;

    /// <summary>
    /// Connection idle timeout in seconds before being closed
    /// </summary>
    [Range(10, 300)]
    public int ConnectionIdleTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Connection lifetime in seconds before forced renewal
    /// </summary>
    [Range(60, 3600)]
    public int ConnectionLifetimeSeconds { get; set; } = 600;

    /// <summary>
    /// Enable connection pooling optimization
    /// </summary>
    public bool EnableConnectionPooling { get; set; } = true;
}

/// <summary>
/// Options for seeding operation
/// </summary>
public class SeederOptions
{
    public int? DriversCount { get; set; }
    public int? VehiclesCount { get; set; }
    public int? BatchSize { get; set; }
    public bool DryRun { get; set; }
    public bool GenerateData { get; set; }
    public bool Interactive { get; set; } = true;
    public string? DealerId { get; set; }
    public string? CustomerName { get; set; }
}

/// <summary>
/// Result of seeding operation
/// </summary>
public class SeederResult
{
    public bool Success { get; set; }
    public string SessionId { get; set; } = string.Empty;
    public int TotalRows { get; set; }
    public int ProcessedRows { get; set; }
    public int SuccessfulRows { get; set; }
    public int FailedRows { get; set; }
    public TimeSpan Duration { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Result of data generation operation
/// </summary>
public class DataGenerationResult
{
    public bool Success { get; set; }
    public int GeneratedRows { get; set; }
    public TimeSpan Duration { get; set; }
    public List<string> Errors { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Result of data validation operation
/// </summary>
public class ValidationResult
{
    public bool Success { get; set; }
    public int ValidRows { get; set; }
    public int InvalidRows { get; set; }
    public List<string> ValidationErrors { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}

/// <summary>
/// Result of data processing operation
/// </summary>
public class ProcessingResult
{
    public bool Success { get; set; }
    public int ProcessedRows { get; set; }
    public int InsertedRows { get; set; }
    public int UpdatedRows { get; set; }
    public int SkippedRows { get; set; }
    public List<string> ProcessingErrors { get; set; } = new();
    public string Summary { get; set; } = string.Empty;
}
