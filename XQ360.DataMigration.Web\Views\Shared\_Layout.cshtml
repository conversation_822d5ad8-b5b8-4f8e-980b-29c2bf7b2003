﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <script type="importmap"></script>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        .auth-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .auth-modal {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 90%;
        }

        .auth-modal h3 {
            color: #333;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .auth-modal .form-control {
            margin-bottom: 1rem;
        }

        .auth-modal .btn-primary {
            width: 100%;
            margin-top: 1rem;
        }

        .auth-error {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            text-align: center;
        }

        .page-content {
            display: none;
        }

        .page-content.authenticated {
            display: block;
        }
    </style>
</head>

<body>
    <!-- Authentication Overlay -->
    <div id="authOverlay" class="auth-overlay">
        <div class="auth-modal">
            <h3><i class="fas fa-lock"></i> Authentication Required</h3>
            <form id="authForm">
                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="username" name="username" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> Login
                </button>
                <div id="authError" class="auth-error" style="display: none;"></div>
            </form>
        </div>
    </div>

    <!-- Page Content (initially hidden) -->
    <div id="pageContent" class="page-content">
        <header>
            <nav
                class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
                <div class="container-fluid">
                    <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">XQ360 Data
                        Migration</a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                        data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent" aria-expanded="false"
                        aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                        <ul class="navbar-nav flex-grow-1">
                            <li class="nav-item">
                                <a class="nav-link text-dark" asp-area="" asp-controller="Home"
                                    asp-action="Index">Home</a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link text-dark" asp-area="" asp-controller="Home"
                                    asp-action="Privacy">Privacy</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>
        </header>
        <div class="container">
            <main role="main" class="pb-3">
                @RenderBody()
            </main>
        </div>

        <footer class="border-top footer text-muted">
            <div class="container">
                &copy; 2025 - XQ360 Data Migration - <a asp-area="" asp-controller="Home"
                    asp-action="Privacy">Privacy</a>
            </div>
        </footer>
    </div>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.0/signalr.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    <!-- Authentication Script -->
    <script>
        $(document).ready(function () {
            // *** TEMPORARY DEVELOPMENT BYPASS ***
            // TODO: Remove this bypass before production deployment
            // Configure in appsettings.json under Authentication:BypassEnabled
            const ENABLE_AUTH_BYPASS = @Html.Raw(ViewBag.AuthBypassEnabled?.ToString().ToLower() ?? "false");

            if (ENABLE_AUTH_BYPASS) {
                console.log('🚨 DEVELOPMENT MODE: Authentication bypass is ENABLED');
                console.log('⚠️  This should be DISABLED in production!');

                // Automatically authenticate and show content
                sessionStorage.setItem('authenticated', 'true');
                showPageContent();
                return;
            }

            // Normal authentication flow (when bypass is disabled)
            console.log('🔒 Authentication required');

            // Check if already authenticated
            if (sessionStorage.getItem('authenticated') === 'true') {
                showPageContent();
                return;
            }

            // Handle authentication form submission
            $('#authForm').on('submit', function (e) {
                e.preventDefault();

                const username = $('#username').val().trim();
                const password = $('#password').val();

                // Hardcoded credentials
                const validUsername = 'admin';
                const validPassword = 'C!admin2025';

                if (username === validUsername && password === validPassword) {
                    // Store authentication state
                    sessionStorage.setItem('authenticated', 'true');

                    // Hide overlay and show content
                    showPageContent();

                    // Clear form
                    $('#authForm')[0].reset();
                    $('#authError').hide();
                } else {
                    // Show error message
                    $('#authError').text('Invalid username or password. Please try again.').show();
                    $('#password').val('').focus();
                }
            });

            function showPageContent() {
                $('#authOverlay').fadeOut(300, function () {
                    $('#pageContent').addClass('authenticated').fadeIn(300);
                });
            }

            // Focus on username field when page loads (only if bypass is disabled)
            if (!ENABLE_AUTH_BYPASS) {
                $('#username').focus();
            }
        });
    </script>

    @await RenderSectionAsync("Scripts", required: false)
</body>

</html>
