using Serilog;
using XQ360.DataMigration.Implementations;
using XQ360.DataMigration.Interfaces;
using XQ360.DataMigration.Services;
using XQ360.DataMigration.Web.Hubs;
using XQ360.DataMigration.Web.Services;
using XQ360.DataMigration.Web.Services.BulkSeeder;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Web.Models;
using Microsoft.Extensions.Options;

var builder = WebApplication.CreateBuilder(args);

// Add Serilog
builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));

// Add services to the container.
builder.Services.AddControllersWithViews();
builder.Services.AddSignalR(options =>
{
    options.EnableDetailedErrors = true;
    options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
    options.KeepAliveInterval = TimeSpan.FromSeconds(15);
});

// Configure environment settings from appsettings.json
builder.Services.Configure<XQ360.DataMigration.Models.EnvironmentConfiguration>(
    builder.Configuration.GetSection("Migration"));

// Configure authentication settings
builder.Services.Configure<XQ360.DataMigration.Web.Models.AuthenticationConfiguration>(
    builder.Configuration.GetSection("Authentication"));

// Configure bulk seeder settings
builder.Services.Configure<BulkSeederConfiguration>(
    builder.Configuration.GetSection(BulkSeederConfiguration.SectionName));

// Register migration services from the main project
builder.Services.AddScoped<MigrationOrchestrator>();
builder.Services.AddScoped<MigrationReportingService>();
builder.Services.AddScoped<EnvironmentConfigurationService>();
builder.Services.AddScoped<IEnvironmentConfigurationService>(provider =>
    provider.GetRequiredService<EnvironmentConfigurationService>());
builder.Services.AddScoped<IPermissionService, PermissionService>();

// Register migration implementations
builder.Services.AddScoped<CardMigration>();
builder.Services.AddScoped<DriverBlacklistMigration>();
builder.Services.AddScoped<PersonMigration>();
builder.Services.AddScoped<PreOpChecklistMigration>();
builder.Services.AddScoped<SpareModuleMigration>();
builder.Services.AddScoped<SupervisorAccessMigration>();
builder.Services.AddScoped<VehicleAccessMigration>();
builder.Services.AddScoped<VehicleMigration>();
builder.Services.AddScoped<VehicleSyncMigration>();
builder.Services.AddScoped<WebsiteUserMigration>();

// Register web-specific services
builder.Services.AddScoped<IMigrationService, MigrationService>();
builder.Services.AddScoped<ICsvFormatValidator, CsvFormatValidator>();

// Register bulk seeder services
builder.Services.AddScoped<IBulkSeederService, BulkSeederService>();
builder.Services.AddScoped<ISqlDataGenerationService, SqlDataGenerationService>();

// Register Phase 1 optimized services - Foundation Infrastructure Enhancement
builder.Services.AddScoped<IStagingSchemaService, StagingSchemaService>();
builder.Services.AddSingleton<IForeignKeyLookupCacheService, ForeignKeyLookupCacheService>();
builder.Services.AddScoped<IBulkInsertOptimizationService, BulkInsertOptimizationService>();
builder.Services.AddSingleton<IConnectionPoolService, ConnectionPoolService>();

// Add HTTP client for API calls - specifically configured for XQ360ApiClient
builder.Services.AddHttpClient<XQ360ApiClient>();

// Configure XQ360ApiClient to get MigrationConfiguration from EnvironmentConfigurationService
builder.Services.AddScoped<IOptions<MigrationConfiguration>>(provider =>
{
    var envService = provider.GetRequiredService<IEnvironmentConfigurationService>();
    var migrationConfig = envService.CurrentMigrationConfiguration;
    return Microsoft.Extensions.Options.Options.Create(migrationConfig);
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();

app.UseAuthorization();

// Add SignalR hub for real-time progress updates
app.MapHub<MigrationHub>("/migrationHub");

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.Run();
