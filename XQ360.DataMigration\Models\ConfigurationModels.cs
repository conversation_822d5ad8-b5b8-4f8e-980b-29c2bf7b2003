﻿using System;
using System.Collections.Generic;

namespace XQ360.DataMigration.Models
{
    public class MigrationConfiguration
    {
        public required string DatabaseConnection { get; set; }
        public required string ApiBaseUrl { get; set; }  
        public required string ApiUsername { get; set; }
        public required string ApiPassword { get; set; }
        public int BatchSize { get; set; } = 100;
        public int MaxRetryAttempts { get; set; } = 3;
        public bool BackupEnabled { get; set; } = true;
        public bool ValidateBeforeMigration { get; set; } = true;
        public bool ContinueOnError { get; set; } = false;
    }

    public class EnvironmentConfiguration
    {
        public Dictionary<string, EnvironmentSettings> Environments { get; set; } = new();
        public int BatchSize { get; set; } = 100;
        public int MaxRetryAttempts { get; set; } = 3;
        public bool BackupEnabled { get; set; } = true;
        public bool ValidateBeforeMigration { get; set; } = true;
        public bool ContinueOnError { get; set; } = false;
    }

    public class EnvironmentSettings
    {
        public required string Name { get; set; }
        public required string DatabaseConnection { get; set; }
        public required string ApiBaseUrl { get; set; }  
        public required string ApiUsername { get; set; }
        public required string ApiPassword { get; set; }
        public string? Description { get; set; }
    }

    public class MigrationResult
    {
        public bool Success { get; set; }
        public int RecordsProcessed { get; set; }
        public int RecordsInserted { get; set; }
        public int RecordsUpdated { get; set; }
        public int RecordsSkipped { get; set; }
        public TimeSpan Duration { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        
        // Enhanced reporting features
        public List<DetailedError> DetailedErrors { get; set; } = new List<DetailedError>();
        public List<DetailedWarning> DetailedWarnings { get; set; } = new List<DetailedWarning>();
        public MigrationSummary Summary { get; set; } = new MigrationSummary();
    }

    public class DetailedError
    {
        public int RowNumber { get; set; }
        public required string ErrorType { get; set; }
        public required string ErrorMessage { get; set; }
        public required string FieldName { get; set; }
        public required string FieldValue { get; set; }
        public required string Suggestion { get; set; }
        public Dictionary<string, string> Context { get; set; } = new Dictionary<string, string>();
    }

    public class DetailedWarning
    {
        public int RowNumber { get; set; }
        public required string WarningType { get; set; }
        public required string WarningMessage { get; set; }
        public required string FieldName { get; set; }
        public required string FieldValue { get; set; }
        public required string Recommendation { get; set; }
        public Dictionary<string, string> Context { get; set; } = new Dictionary<string, string>();
    }

    public class MigrationSummary
    {
        public Dictionary<string, int> SuccessBreakdown { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> ErrorBreakdown { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> WarningBreakdown { get; set; } = new Dictionary<string, int>();
        public List<string> CreatedRelationships { get; set; } = new List<string>();
        public List<string> UpdatedRecords { get; set; } = new List<string>();
        public List<string> SkippedReasons { get; set; } = new List<string>();
    }

    // Error type constants for consistent reporting
    public static class ErrorTypes
    {
        public const string MISSING_PERSON = "MISSING_PERSON";
        public const string DUPLICATE_CARD = "DUPLICATE_CARD";
        public const string INVALID_DEALER = "INVALID_DEALER";
        public const string INVALID_CUSTOMER = "INVALID_CUSTOMER";
        public const string INVALID_SITE = "INVALID_SITE";
        public const string INVALID_DEPARTMENT = "INVALID_DEPARTMENT";
        public const string INVALID_MODEL = "INVALID_MODEL";
        public const string MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD";
        public const string INVALID_DATA_FORMAT = "INVALID_DATA_FORMAT";
        public const string BUSINESS_RULE_VIOLATION = "BUSINESS_RULE_VIOLATION";
        public const string DUPLICATE_RECORD = "DUPLICATE_RECORD";
        public const string FOREIGN_KEY_CONSTRAINT = "FOREIGN_KEY_CONSTRAINT";
        public const string DATABASE_ERROR = "DATABASE_ERROR";
    }

    public static class WarningTypes
    {
        public const string EXISTING_RECORD_SKIPPED = "EXISTING_RECORD_SKIPPED";
        public const string AUTO_GENERATED_VALUE = "AUTO_GENERATED_VALUE";
        public const string DEFAULT_VALUE_USED = "DEFAULT_VALUE_USED";
        public const string RELATIONSHIP_CREATED = "RELATIONSHIP_CREATED";
        public const string PARTIAL_MATCH = "PARTIAL_MATCH";
        public const string DEPRECATED_FIELD = "DEPRECATED_FIELD";
        public const string MISSING_DEPENDENCY = "MISSING_DEPENDENCY";
        public const string VALIDATION_WARNING = "VALIDATION_WARNING";
    }
}