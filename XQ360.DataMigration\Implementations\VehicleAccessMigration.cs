using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CsvHelper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using XQ360.DataMigration.Interfaces;
using XQ360.DataMigration.Models;
using XQ360.DataMigration.Services;

namespace XQ360.DataMigration.Implementations
{
    public class VehicleAccessMigration : ICoordinatedMigration
    {
        private readonly ILogger<VehicleAccessMigration> _logger;
        private readonly MigrationConfiguration _config;
        private readonly string _connectionString;
        private readonly IPermissionService _permissionService;
        private readonly MigrationReportingService _reportingService;

        // Thread-safe cache for database lookups to avoid repeated queries
        private readonly ConcurrentDictionary<string, Guid> _siteCache = new ConcurrentDictionary<string, Guid>();
        private readonly ConcurrentDictionary<string, Guid> _dealerCache = new ConcurrentDictionary<string, Guid>();
        private readonly ConcurrentDictionary<string, Guid> _customerCache = new ConcurrentDictionary<string, Guid>();

        public VehicleAccessMigration(
            ILogger<VehicleAccessMigration> logger,
            IOptions<MigrationConfiguration> config,
            IPermissionService permissionService,
            MigrationReportingService reportingService)
        {
            _logger = logger;
            _config = config.Value;
            _connectionString = _config.DatabaseConnection;
            _permissionService = permissionService;
            _reportingService = reportingService;
        }

        public async Task<MigrationResult> ExecuteAsync(string csvFilePath, string? accessLevel = null)
        {
            var result = new MigrationResult();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("Starting Vehicle Access migration using direct SQL approach");

                // Step 1: Process CSV file
                var data = await ProcessCsvFileAsync(csvFilePath);
                if (data.Count == 0)
                {
                    _logger.LogWarning("No data found in CSV file");
                    return new MigrationResult { Success = true, RecordsProcessed = 0 };
                }

                // Step 2: Group records by AccessLevel and execute migration for each group
                var migrationResult = await ExecuteVehicleAccessByAccessLevelAsync(data);

                // Step 3: Clean up duplicates
                await CleanupDuplicateAccessRecordsAsync();

                result.Success = migrationResult.Success;
                result.RecordsProcessed = data.Count;
                result.RecordsInserted = migrationResult.RecordsInserted;
                result.RecordsSkipped = data.Count - migrationResult.RecordsInserted;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors = migrationResult.Errors;
                result.Warnings = migrationResult.Warnings;
                
                // Transfer detailed errors and warnings for Full Migration Report
                result.DetailedErrors = migrationResult.DetailedErrors;
                result.DetailedWarnings = migrationResult.DetailedWarnings;

                _logger.LogInformation($"Vehicle Access migration completed: {result.RecordsInserted} inserted, {result.RecordsSkipped} skipped, Duration: {result.Duration}");

                // Generate comprehensive report
                _reportingService.GenerateMigrationReport(result, "Vehicle Access Migration");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Vehicle Access migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message },
                    Duration = DateTime.UtcNow - startTime
                };
            }
        }

        /// <summary>
        /// Execute Vehicle Access migration using an external transaction for coordination with other migrations
        /// </summary>
        public async Task<MigrationResult> ExecuteWithTransactionAsync(string csvFilePath, SqlConnection connection, SqlTransaction transaction)
        {
            var result = new MigrationResult();
            var startTime = DateTime.UtcNow;

            try
            {
                _logger.LogInformation("Starting Vehicle Access migration using coordinated transaction");

                // Step 1: Process CSV file
                var data = await ProcessCsvFileAsync(csvFilePath);
                if (data.Count == 0)
                {
                    _logger.LogWarning("No data found in CSV file");
                    return new MigrationResult { Success = true, RecordsProcessed = 0 };
                }

                // Step 2: Group records by AccessLevel and execute migration for each group
                var migrationResult = await ExecuteVehicleAccessByAccessLevelAsync(data, connection, transaction);

                // Note: Skipping duplicate cleanup for coordinated transaction - caller should handle this
                // await CleanupDuplicateAccessRecordsAsync();

                result.Success = migrationResult.Success;
                result.RecordsProcessed = data.Count;
                result.RecordsInserted = migrationResult.RecordsInserted;
                result.RecordsSkipped = data.Count - migrationResult.RecordsInserted;
                result.Duration = DateTime.UtcNow - startTime;
                result.Errors = migrationResult.Errors;
                result.Warnings = migrationResult.Warnings;

                _logger.LogInformation($"Coordinated Vehicle Access migration completed: {result.RecordsInserted} inserted, {result.RecordsSkipped} skipped, Duration: {result.Duration}");

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Coordinated Vehicle Access migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message },
                    Duration = DateTime.UtcNow - startTime
                };
            }
        }

        private async Task<List<CardImportModel>> ProcessCsvFileAsync(string csvFilePath)
        {
            _logger.LogInformation("Processing Vehicle Access CSV file...");

            using var fileStream = new FileStream(csvFilePath, FileMode.Open, FileAccess.Read, FileShare.Read, bufferSize: 4096, useAsync: true);
            using var streamReader = new StreamReader(fileStream, System.Text.Encoding.UTF8);
            using var csv = new CsvReader(streamReader, CultureInfo.InvariantCulture);

            var records = csv.GetRecords<CardImportModel>().ToList();

            _logger.LogInformation($"Processed {records.Count} records from CSV");
            return records;
        }

        private async Task<MigrationResult> ExecuteVehicleAccessByAccessLevelAsync(List<CardImportModel> data)
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var transaction = connection.BeginTransaction();

            try
            {
                var result = await ExecuteVehicleAccessByAccessLevelAsync(data, connection, transaction);
                await transaction.CommitAsync();
                return result;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        private async Task<MigrationResult> ExecuteVehicleAccessByAccessLevelAsync(List<CardImportModel> data, SqlConnection connection, SqlTransaction transaction)
        {
            _logger.LogInformation("Grouping records by AccessLevel and executing vehicle access migration...");

            var result = new MigrationResult
            {
                Success = true,
                RecordsProcessed = data.Count,
                Errors = new List<string>(),
                Warnings = new List<string>()
            };

            // Group records by AccessLevel
            var accessLevelGroups = data.GroupBy(d => d.AccessLevel?.ToLower() ?? "department").ToList();

            _logger.LogInformation($"Found {accessLevelGroups.Count} access level groups:");
            foreach (var group in accessLevelGroups)
            {
                _logger.LogDebug($"  - {group.Key}: {group.Count()} records");
            }

            try
            {
                var totalInserted = 0;

                foreach (var group in accessLevelGroups)
                {
                    var accessLevel = group.Key;
                    var groupData = group.ToList();

                    _logger.LogInformation($"Processing {groupData.Count} records for '{accessLevel}' access level...");
                    _logger.LogDebug($"Note: Including both newly created cards and existing cards that were skipped during card creation");

                    var groupResult = await ExecuteVehicleAccessSqlAsync(groupData, accessLevel, connection, transaction);
                    
                    totalInserted += groupResult.RecordsInserted;
                    result.Errors.AddRange(groupResult.Errors);
                    result.Warnings.AddRange(groupResult.Warnings);

                    if (!groupResult.Success)
                    {
                        result.Success = false;
                    }
                }

                // Note: Transaction commit/rollback handled by external caller

                result.RecordsInserted = totalInserted;
                result.RecordsSkipped = result.RecordsProcessed - totalInserted;

                _logger.LogInformation($"Successfully created {totalInserted} vehicle access records across all access levels");

                return result;
            }
            catch (Exception ex)
            {
                // Note: Transaction rollback handled by external caller
                _logger.LogError(ex, "Vehicle Access migration failed");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        private async Task<MigrationResult> ExecuteVehicleAccessSqlAsync(List<CardImportModel> data, string accessLevel, SqlConnection connection, SqlTransaction transaction)
        {
            _logger.LogInformation($"Executing Vehicle Access SQL migration for {accessLevel} level...");

            try
            {
                var insertedCount = 0;
                var warnings = new List<string>();

                switch (accessLevel.ToLower())
                {
                    case "site":
                        insertedCount = await CreateSiteAccessAsync(data, connection, transaction, warnings);
                        break;
                    case "department":
                        insertedCount = await CreateDepartmentAccessAsync(data, connection, transaction, warnings);
                        break;
                    case "model":
                        insertedCount = await CreateModelAccessAsync(data, connection, transaction, warnings);
                        break;
                    case "vehicle":
                        insertedCount = await CreateVehicleAccessAsync(data, connection, transaction, warnings);
                        break;
                    default:
                        throw new ArgumentException($"Invalid access level: {accessLevel}");
                }

                // Note: Transaction commit is handled by the caller

                _logger.LogInformation($"Successfully created {insertedCount} {accessLevel} access records");

                return new MigrationResult
                {
                    Success = true,
                    RecordsInserted = insertedCount,
                    Warnings = warnings
                };
            }
            catch (Exception ex)
            {
                // Note: Transaction rollback is handled by the caller
                _logger.LogError(ex, $"Vehicle Access SQL migration failed for {accessLevel} level");
                return new MigrationResult
                {
                    Success = false,
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        private async Task<int> CreateSiteAccessAsync(List<CardImportModel> data, SqlConnection connection, SqlTransaction transaction, List<string> warnings)
        {
            _logger.LogDebug("Creating Site Vehicle Access records (Comprehensive: Department + Model + Vehicle access)...");

            int totalInserted = 0;
            var normalDriverPermissionId = await _permissionService.GetNormalDriverPermissionIdAsync();

            // Group data by site to get unique site IDs
            var siteGroups = data.GroupBy(d => new { Site = d.Site, Customer = d.Customer }).ToList();

            foreach (var siteGroup in siteGroups)
            {
                try
                {
                    var siteId = await GetSiteIdAsync(siteGroup.Key.Site, siteGroup.Key.Customer, connection, transaction);
                    var driverIds = await GetDriverIdsForCardImportAsync(siteGroup.ToList(), connection, transaction);

                    if (driverIds.Count == 0)
                    {
                        warnings.Add($"No drivers found for site {siteGroup.Key.Site}");
                        continue;
                    }

                    // Create parameterized IN clause for driver IDs (SQL injection safe)
                    var driverParameters = string.Join(",", driverIds.Select((_, i) => $"@driverId{i}"));

                    // PART 0: Create SiteVehicleNormalCardAccess (site access itself)
                    var siteAccessSql = $@"
                        INSERT INTO [dbo].[SiteVehicleNormalCardAccess] (Id, SiteId, PermissionId, CardId)
                        SELECT
                            NEWID() AS Id,
                            @SiteId AS SiteId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId
                        FROM [dbo].[Driver] d
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[SiteVehicleNormalCardAccess] svnca
                            WHERE svnca.SiteId = @SiteId
                            AND svnca.CardId = d.CardDetailsId
                            AND svnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(siteAccessSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@SiteId", siteId);
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);
                        
                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }
                        
                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    // PART 1: Create DepartmentVehicleNormalCardAccess for all departments in the site
                    var departmentSql = $@"
                        INSERT INTO [dbo].[DepartmentVehicleNormalCardAccess] (Id, DepartmentId, PermissionId, CardId)
                        SELECT DISTINCT
                            NEWID() AS Id,
                            dept.Id AS DepartmentId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Department] dept ON dept.SiteId = @SiteId
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[DepartmentVehicleNormalCardAccess] dvnca
                            WHERE dvnca.DepartmentId = dept.Id
                            AND dvnca.CardId = d.CardDetailsId
                            AND dvnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(departmentSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@SiteId", siteId);
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);
                        
                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }
                        
                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    // PART 2: Create ModelVehicleNormalCardAccess for all models in all departments of the site
                    var modelSql = $@"
                        INSERT INTO [dbo].[ModelVehicleNormalCardAccess] (Id, ModelId, PermissionId, CardId, DepartmentId)
                        SELECT DISTINCT
                            NEWID() AS Id,
                            v.ModelId AS ModelId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId,
                            v.DepartmentId AS DepartmentId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Department] dept ON dept.SiteId = @SiteId
                        INNER JOIN [dbo].[Vehicle] v ON v.DepartmentId = dept.Id
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND v.ModelId IS NOT NULL
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[ModelVehicleNormalCardAccess] mvnca
                            WHERE mvnca.ModelId = v.ModelId
                            AND mvnca.CardId = d.CardDetailsId
                            AND mvnca.DepartmentId = v.DepartmentId
                            AND mvnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(modelSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@SiteId", siteId);
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);
                        
                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }
                        
                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    // PART 3: Create PerVehicleNormalCardAccess for all vehicles in all departments of the site
                    var vehicleSql = $@"
                        INSERT INTO [dbo].[PerVehicleNormalCardAccess] (Id, VehicleId, PermissionId, CardId)
                        SELECT 
                            NEWID() AS Id,
                            v.Id AS VehicleId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Department] dept ON dept.SiteId = @SiteId
                        INNER JOIN [dbo].[Vehicle] v ON v.DepartmentId = dept.Id
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[PerVehicleNormalCardAccess] pvnca
                            WHERE pvnca.VehicleId = v.Id
                            AND pvnca.CardId = d.CardDetailsId
                            AND pvnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(vehicleSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@SiteId", siteId);
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);
                        
                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }
                        
                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    _logger.LogDebug($"Created site-level access for {driverIds.Count} drivers in site {siteGroup.Key.Site}");
                }
                catch (Exception ex)
                {
                    warnings.Add($"Failed to create site access for site {siteGroup.Key.Site}: {ex.Message}");
                }
            }

            return totalInserted;
        }

        private async Task<List<Guid>> GetDriverIdsForCardImportAsync(List<CardImportModel> cardData, SqlConnection connection, SqlTransaction transaction)
        {
            var driverIds = new List<Guid>();

            foreach (var record in cardData)
            {
                try
                {
                    // Enhanced query to find drivers and ensure they have cards
                    var sql = @"
                        SELECT p.DriverId 
                        FROM [dbo].[Person] p
                        INNER JOIN [dbo].[Driver] dr ON p.DriverId = dr.Id
                        INNER JOIN [dbo].[Department] d ON p.DepartmentId = d.Id
                        INNER JOIN [dbo].[Site] s ON d.SiteId = s.Id
                        INNER JOIN [dbo].[Customer] c ON s.CustomerId = c.Id
                        WHERE p.FirstName = @FirstName 
                        AND p.LastName = @LastName
                        AND d.Name = @DepartmentName
                        AND s.Name = @SiteName
                        AND c.CompanyName = @CustomerName
                        AND p.DriverId IS NOT NULL
                        AND dr.CardDetailsId IS NOT NULL";

                    using var cmd = new SqlCommand(sql, connection, transaction);
                    cmd.Parameters.AddWithValue("@FirstName", record.FirstName);
                    cmd.Parameters.AddWithValue("@LastName", record.LastName);
                    cmd.Parameters.AddWithValue("@DepartmentName", record.DepartmentName);
                    cmd.Parameters.AddWithValue("@SiteName", record.Site);
                    cmd.Parameters.AddWithValue("@CustomerName", record.Customer);

                    var result = await cmd.ExecuteScalarAsync();
                    if (result != null && result != DBNull.Value)
                    {
                        var driverId = (Guid)result;
                        driverIds.Add(driverId);
                    }
                    else
                    {
                        _logger.LogWarning($"Driver {record.FirstName} {record.LastName} not found or doesn't have a card assigned");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"Failed to get driver ID for {record.FirstName} {record.LastName}: {ex.Message}");
                }
            }

            var uniqueDriverIds = driverIds.Distinct().ToList();
            _logger.LogDebug($"Found {uniqueDriverIds.Count} drivers with cards from {cardData.Count} CSV records");
            
            return uniqueDriverIds;
        }

        private async Task<int> CreateDepartmentAccessAsync(List<CardImportModel> data, SqlConnection connection, SqlTransaction transaction, List<string> warnings)
        {
            _logger.LogDebug("Creating Department Vehicle Access records (Comprehensive: Site + Department + Model + Vehicle access)...");

            int totalInserted = 0;
            var normalDriverPermissionId = await _permissionService.GetNormalDriverPermissionIdAsync();

            // Group data by department to get unique department access
            var departmentGroups = data.GroupBy(d => new { Site = d.Site, Customer = d.Customer, Department = d.DepartmentName }).ToList();

            foreach (var deptGroup in departmentGroups)
            {
                try
                {
                    var siteId = await GetSiteIdAsync(deptGroup.Key.Site, deptGroup.Key.Customer, connection, transaction);
                    var driverIds = await GetDriverIdsForCardImportAsync(deptGroup.ToList(), connection, transaction);

                    if (driverIds.Count == 0)
                    {
                        warnings.Add($"No drivers found for department {deptGroup.Key.Department} in site {deptGroup.Key.Site}");
                        continue;
                    }

                    // Create parameterized IN clause for driver IDs (SQL injection safe)
                    var driverParameters = string.Join(",", driverIds.Select((_, i) => $"@driverId{i}"));

                    // PART 1: Create SiteVehicleNormalCardAccess (Dept Level) 
                    var siteAccessSql = $@"
                        INSERT INTO [dbo].[SiteVehicleNormalCardAccess] (Id, SiteId, PermissionId, CardId)
                        SELECT
                            NEWID() AS Id,
                            @SiteId AS SiteId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId
                        FROM [dbo].[Driver] d
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[SiteVehicleNormalCardAccess] svnca
                            WHERE svnca.SiteId = @SiteId
                            AND svnca.CardId = d.CardDetailsId
                            AND svnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(siteAccessSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@SiteId", siteId);
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);
                        
                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }
                        
                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    // PART 2: Create DepartmentVehicleNormalCardAccess 
                    var departmentAccessSql = $@"
                        INSERT INTO [dbo].[DepartmentVehicleNormalCardAccess] (Id, DepartmentId, PermissionId, CardId)
                        SELECT
                            NEWID() AS Id,
                            p.DepartmentId AS DepartmentId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Person] p ON d.Id = p.DriverId
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND p.DepartmentId IS NOT NULL
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[DepartmentVehicleNormalCardAccess] dvnca
                            WHERE dvnca.DepartmentId = p.DepartmentId
                            AND dvnca.CardId = d.CardDetailsId
                            AND dvnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(departmentAccessSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);
                        
                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }
                        
                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    // PART 3: Create ModelVehicleNormalCardAccess for all models in the department
                    var modelAccessSql = $@"
                        INSERT INTO [dbo].[ModelVehicleNormalCardAccess] (Id, ModelId, PermissionId, CardId, DepartmentId)
                        SELECT DISTINCT
                            NEWID() AS Id,
                            v.ModelId AS ModelId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId,
                            p.DepartmentId AS DepartmentId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Person] p ON d.Id = p.DriverId
                        INNER JOIN [dbo].[Vehicle] v ON v.DepartmentId = p.DepartmentId
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND p.DepartmentId IS NOT NULL
                        AND v.ModelId IS NOT NULL
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[ModelVehicleNormalCardAccess] mvnca
                            WHERE mvnca.ModelId = v.ModelId
                            AND mvnca.CardId = d.CardDetailsId
                            AND mvnca.DepartmentId = p.DepartmentId
                            AND mvnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(modelAccessSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);
                        
                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }
                        
                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    // PART 4: Create PerVehicleNormalCardAccess for all vehicles in the department
                    var vehicleAccessSql = $@"
                        INSERT INTO [dbo].[PerVehicleNormalCardAccess] (Id, VehicleId, PermissionId, CardId)
                        SELECT 
                            NEWID() AS Id,
                            v.Id AS VehicleId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Person] p ON d.Id = p.DriverId
                        INNER JOIN [dbo].[Vehicle] v ON v.DepartmentId = p.DepartmentId
                        WHERE d.CardDetailsId IS NOT NULL
                        AND d.Id IN ({driverParameters})
                        AND p.DepartmentId IS NOT NULL
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[PerVehicleNormalCardAccess] pvnca
                            WHERE pvnca.VehicleId = v.Id
                            AND pvnca.CardId = d.CardDetailsId
                            AND pvnca.PermissionId = @PermissionId
                        );";

                    using (var cmd = new SqlCommand(vehicleAccessSql, connection, transaction))
                    {
                        cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);
                        
                        // Add parameterized driver IDs (SQL injection safe)
                        for (int i = 0; i < driverIds.Count; i++)
                        {
                            cmd.Parameters.AddWithValue($"@driverId{i}", driverIds[i]);
                        }
                        
                        totalInserted += await cmd.ExecuteNonQueryAsync();
                    }

                    _logger.LogDebug($"Created department-level access for {driverIds.Count} drivers in department {deptGroup.Key.Department}");
                }
                catch (Exception ex)
                {
                    warnings.Add($"Failed to create department access for department {deptGroup.Key.Department}: {ex.Message}");
                }
            }

            return totalInserted;
        }

        private async Task<int> CreateModelAccessAsync(List<CardImportModel> data, SqlConnection connection, SqlTransaction transaction, List<string> warnings)
        {
            _logger.LogDebug("Creating Model Vehicle Access records...");

            int totalInserted = 0;
            var normalDriverPermissionId = await _permissionService.GetNormalDriverPermissionIdAsync();

            // Group data by site to get unique site IDs
            var siteGroups = data.GroupBy(d => new { Site = d.Site, Customer = d.Customer }).ToList();

            foreach (var siteGroup in siteGroups)
            {
                try
                {
                    // Skip groups with null Site or Customer
                    if (string.IsNullOrEmpty(siteGroup.Key.Site) || string.IsNullOrEmpty(siteGroup.Key.Customer))
                    {
                        warnings.Add($"Skipping group with null/empty Site ({siteGroup.Key.Site}) or Customer ({siteGroup.Key.Customer})");
                        continue;
                    }

                    var siteId = await GetSiteIdAsync(siteGroup.Key.Site, siteGroup.Key.Customer, connection, transaction);
                    
                    // Create parameterized name pairs (SQL injection safe)
                    var nameParameters = string.Join(", ", siteGroup.Select((_, i) => $"(@firstName{i}, @lastName{i})"));

                    var sql = $@"
                        INSERT INTO [dbo].[ModelVehicleNormalCardAccess] (Id, ModelId, PermissionId, CardId, DepartmentId)
                        SELECT DISTINCT
                            NEWID() AS Id,
                            v.ModelId AS ModelId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId,
                            dept.Id AS DepartmentId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Person] p ON d.Id = p.DriverId
                        INNER JOIN [dbo].[Department] dept ON p.DepartmentId = dept.Id
                        INNER JOIN [dbo].[Vehicle] v ON v.DepartmentId = dept.Id
                        WHERE d.CardDetailsId IS NOT NULL
                        AND dept.SiteId = @SiteId
                        AND v.ModelId IS NOT NULL
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[ModelVehicleNormalCardAccess] mvnca
                            WHERE mvnca.ModelId = v.ModelId
                            AND mvnca.CardId = d.CardDetailsId
                            AND mvnca.DepartmentId = dept.Id
                            AND mvnca.PermissionId = @PermissionId
                        )
                        AND p.FirstName + ' ' + p.LastName IN (
                            SELECT FirstName + ' ' + LastName 
                            FROM (VALUES {nameParameters}) AS V(FirstName, LastName)
                        );";

                    using var cmd = new SqlCommand(sql, connection, transaction);
                    cmd.Parameters.AddWithValue("@SiteId", siteId);
                    cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);
                    
                    // Add parameterized name pairs (SQL injection safe)
                    var siteGroupList = siteGroup.ToList();
                    for (int i = 0; i < siteGroupList.Count; i++)
                    {
                        cmd.Parameters.AddWithValue($"@firstName{i}", siteGroupList[i].FirstName);
                        cmd.Parameters.AddWithValue($"@lastName{i}", siteGroupList[i].LastName);
                    }

                    totalInserted += await cmd.ExecuteNonQueryAsync();
                }
                catch (Exception ex)
                {
                    warnings.Add($"Failed to create model access for site {siteGroup.Key.Site}: {ex.Message}");
                }
            }

            return totalInserted;
        }

        private async Task<int> CreateVehicleAccessAsync(List<CardImportModel> data, SqlConnection connection, SqlTransaction transaction, List<string> warnings)
        {
            _logger.LogDebug("Creating Per Vehicle Access records...");

            int totalInserted = 0;
            var normalDriverPermissionId = await _permissionService.GetNormalDriverPermissionIdAsync();

            // Group data by site to get unique site IDs
            var siteGroups = data.GroupBy(d => new { Site = d.Site, Customer = d.Customer }).ToList();

            foreach (var siteGroup in siteGroups)
            {
                try
                {
                    // Skip groups with null Site or Customer
                    if (string.IsNullOrEmpty(siteGroup.Key.Site) || string.IsNullOrEmpty(siteGroup.Key.Customer))
                    {
                        warnings.Add($"Skipping group with null/empty Site ({siteGroup.Key.Site}) or Customer ({siteGroup.Key.Customer})");
                        continue;
                    }

                    var siteId = await GetSiteIdAsync(siteGroup.Key.Site, siteGroup.Key.Customer, connection, transaction);
                    
                    // Create parameterized name pairs (SQL injection safe)
                    var nameParameters = string.Join(", ", siteGroup.Select((_, i) => $"(@firstName{i}, @lastName{i})"));

                    var sql = $@"
                        INSERT INTO [dbo].[PerVehicleNormalCardAccess] (Id, VehicleId, PermissionId, CardId)
                        SELECT 
                            NEWID() AS Id,
                            v.Id AS VehicleId,
                            @PermissionId AS PermissionId,
                            d.CardDetailsId AS CardId
                        FROM [dbo].[Driver] d
                        INNER JOIN [dbo].[Person] p ON d.Id = p.DriverId
                        INNER JOIN [dbo].[Department] dept ON p.DepartmentId = dept.Id
                        INNER JOIN [dbo].[Vehicle] v ON v.DepartmentId = dept.Id
                        WHERE d.CardDetailsId IS NOT NULL
                        AND dept.SiteId = @SiteId
                        AND NOT EXISTS (
                            SELECT 1 
                            FROM [dbo].[PerVehicleNormalCardAccess] pvnca
                            WHERE pvnca.VehicleId = v.Id
                            AND pvnca.CardId = d.CardDetailsId
                            AND pvnca.PermissionId = @PermissionId
                        )
                        AND p.FirstName + ' ' + p.LastName IN (
                            SELECT FirstName + ' ' + LastName 
                            FROM (VALUES {nameParameters}) AS V(FirstName, LastName)
                        );";

                    using var cmd = new SqlCommand(sql, connection, transaction);
                    cmd.Parameters.AddWithValue("@SiteId", siteId);
                    cmd.Parameters.AddWithValue("@PermissionId", normalDriverPermissionId);
                    
                    // Add parameterized name pairs (SQL injection safe)
                    var siteGroupList = siteGroup.ToList();
                    for (int i = 0; i < siteGroupList.Count; i++)
                    {
                        cmd.Parameters.AddWithValue($"@firstName{i}", siteGroupList[i].FirstName);
                        cmd.Parameters.AddWithValue($"@lastName{i}", siteGroupList[i].LastName);
                    }

                    totalInserted += await cmd.ExecuteNonQueryAsync();
                }
                catch (Exception ex)
                {
                    warnings.Add($"Failed to create vehicle access for site {siteGroup.Key.Site}: {ex.Message}");
                }
            }

            return totalInserted;
        }

        private async Task<Guid> GetSiteIdAsync(string siteName, string customerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var key = $"{siteName}|{customerName}";
            if (_siteCache.TryGetValue(key, out var cachedSiteId))
                return cachedSiteId;

            var customerId = await GetCustomerIdByNameAsync(customerName, connection, transaction);

            var sql = "SELECT Id FROM dbo.Site WHERE Name = @Name AND CustomerId = @CustomerId";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Name", siteName);
            cmd.Parameters.AddWithValue("@CustomerId", customerId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Site '{siteName}' not found for customer '{customerName}'");

            var siteId = (Guid)result;
            _siteCache.TryAdd(key, siteId);
            return siteId;
        }

        private async Task<Guid> GetDealerIdAsync(string dealerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            if (_dealerCache.TryGetValue(dealerName, out var cachedDealerId))
                return cachedDealerId;

            var sql = "SELECT Id FROM dbo.Dealer WHERE Name = @Name";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@Name", dealerName);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Dealer '{dealerName}' not found");

            var dealerId = (Guid)result;
            _dealerCache.TryAdd(dealerName, dealerId);
            return dealerId;
        }

        private async Task<Guid> GetCustomerIdAsync(string customerName, string dealerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var key = $"{customerName}|{dealerName}";
            if (_customerCache.TryGetValue(key, out var cachedCustomerId))
                return cachedCustomerId;

            var dealerId = await GetDealerIdAsync(dealerName, connection, transaction);

            var sql = "SELECT Id FROM dbo.Customer WHERE CompanyName = @CompanyName AND DealerId = @DealerId";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@CompanyName", customerName);
            cmd.Parameters.AddWithValue("@DealerId", dealerId);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Customer '{customerName}' not found for dealer '{dealerName}'");

            var customerId = (Guid)result;
            _customerCache.TryAdd(key, customerId);
            return customerId;
        }

        private async Task<Guid> GetCustomerIdByNameAsync(string customerName, SqlConnection connection, SqlTransaction? transaction = null)
        {
            var sql = "SELECT Id FROM dbo.Customer WHERE CompanyName = @CompanyName";
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@CompanyName", customerName);

            var result = await cmd.ExecuteScalarAsync();
            if (result == null)
                throw new InvalidOperationException($"Customer '{customerName}' not found");

            return (Guid)result;
        }

        public async Task CleanupDuplicateAccessRecordsAsync()
        {
            _logger.LogInformation("Cleaning up duplicate access records...");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();

            try
            {
                // Clean up SiteVehicleNormalCardAccess duplicates
                var siteCleanupSql = @"
                    WITH DuplicateCTE AS (
                        SELECT 
                            Id
                        FROM (
                            SELECT 
                                Id,
                                ROW_NUMBER() OVER (
                                    PARTITION BY PermissionId, SiteId, CardId 
                                    ORDER BY Id
                                ) AS RowNum
                            FROM [SiteVehicleNormalCardAccess]
                        ) AS DupRows
                        WHERE RowNum > 1
                    )
                    DELETE FROM [SiteVehicleNormalCardAccess]
                    WHERE Id IN (SELECT Id FROM DuplicateCTE);";

                using (var cmd = new SqlCommand(siteCleanupSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                // Clean up DepartmentVehicleNormalCardAccess duplicates
                var departmentCleanupSql = @"
                    WITH DuplicateCTE AS (
                        SELECT 
                            Id
                        FROM (
                            SELECT 
                                Id,
                                ROW_NUMBER() OVER (
                                    PARTITION BY CardId, DepartmentId, PermissionId 
                                    ORDER BY Id
                                ) AS RowNum
                            FROM [DepartmentVehicleNormalCardAccess]
                        ) AS DupRows
                        WHERE RowNum > 1
                    )
                    DELETE FROM [DepartmentVehicleNormalCardAccess]
                    WHERE Id IN (SELECT Id FROM DuplicateCTE);";

                using (var cmd = new SqlCommand(departmentCleanupSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                // Clean up ModelVehicleNormalCardAccess duplicates
                var modelCleanupSql = @"
                    WITH DuplicateCTE AS (
                        SELECT 
                            Id
                        FROM (
                            SELECT 
                                Id,
                                ROW_NUMBER() OVER (
                                    PARTITION BY PermissionId, CardId, DepartmentId, ModelId 
                                    ORDER BY Id
                                ) AS RowNum
                            FROM [ModelVehicleNormalCardAccess]
                        ) AS DupRows
                        WHERE RowNum > 1
                    )
                    DELETE FROM [ModelVehicleNormalCardAccess]
                    WHERE Id IN (SELECT Id FROM DuplicateCTE);";

                using (var cmd = new SqlCommand(modelCleanupSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                // Clean up PerVehicleNormalCardAccess duplicates
                var vehicleCleanupSql = @"
                    WITH DuplicateCTE AS (
                        SELECT 
                            Id
                        FROM (
                            SELECT 
                                Id,
                                ROW_NUMBER() OVER (
                                    PARTITION BY PermissionId, VehicleId, CardId 
                                    ORDER BY Id
                                ) AS RowNum
                            FROM [PerVehicleNormalCardAccess]
                        ) AS DupRows
                        WHERE RowNum > 1
                    )
                    DELETE FROM [PerVehicleNormalCardAccess]
                    WHERE Id IN (SELECT Id FROM DuplicateCTE);";

                using (var cmd = new SqlCommand(vehicleCleanupSql, connection, transaction))
                {
                    await cmd.ExecuteNonQueryAsync();
                }

                await transaction.CommitAsync();

                _logger.LogInformation("Successfully cleaned up duplicate access records");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Failed to clean up duplicate access records");
                throw;
            }
        }
    }
}